<template>
  <ContentWrap>
    <div class="order-detail">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h2>订单详情</h2>
          <template v-if="orderData.status === 'pending'">
            <el-tag type="warning" class="status-tag">待支付</el-tag>
          </template>
          <template v-else-if="orderData.status === 'paid'">
            <el-tag type="success" class="status-tag">已支付</el-tag>
          </template>
          <template v-else-if="orderData.status === 'completed'">
            <el-tag type="info" class="status-tag">已完成</el-tag>
          </template>
          <template v-else-if="orderData.status === 'cancelled'">
            <el-tag type="danger" class="status-tag">已取消</el-tag>
          </template>
          <template v-else>
            <el-tag type="info" class="status-tag">未知</el-tag>
          </template>
        </div>
        <div class="header-right">
          <el-button @click="goBack" :icon="ArrowLeftBold">返回</el-button>
          <el-button v-if="canChangeStatus" type="primary" @click="handleStatusChange" :loading="statusLoading">
            {{ orderData.status === 'pending' ? '支付订单' : '完成订单' }}
          </el-button>
        </div>
      </div>

      <!-- 订单信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="订单编号">{{ orderData.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单类型">
            <el-tag :type="orderData.orderType === 'rent' ? 'success' : 'danger'">
              {{ orderData.orderType === 'rent' ? '租车' : '购车' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <template v-if="orderData.status === 'pending'">
              <el-tag type="warning">待支付</el-tag>
            </template>
            <template v-else-if="orderData.status === 'paid'">
              <el-tag type="success">已支付</el-tag>
            </template>
            <template v-else-if="orderData.status === 'completed'">
              <el-tag type="info">已完成</el-tag>
            </template>
            <template v-else-if="orderData.status === 'cancelled'">
              <el-tag type="danger">已取消</el-tag>
            </template>
            <template v-else>
              <el-tag type="info">未知</el-tag>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ orderData.amount }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag :type="orderData.payType === 'alipay' ? 'success' : 'danger'">
              {{ orderData.payType === 'alipay' ? '支付宝' : '微信' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="消费方式">
            <el-tag :type="orderData.rentPaymentMode === 'prepay' ? 'success' : 'danger'">
              {{ orderData.rentPaymentMode === 'prepay' ? '先付后用' : '先用后付' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付宝交易凭证号">{{ orderData.alipayTradeNo || '--' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(orderData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间" v-if="orderData.payTime">{{ formatDate(orderData.payTime)
            }}</el-descriptions-item>
          <el-descriptions-item label="完成时间" v-if="orderData.finishTime">{{ formatDate(orderData.finishTime)
            }}</el-descriptions-item>
          <el-descriptions-item label="取消时间" v-if="orderData.cancelTime">{{ formatDate(orderData.cancelTime)
            }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 租赁信息卡片 -->
      <el-card class="detail-card" shadow="hover" v-if="orderData.orderType === 'rent'">
        <template #header>
          <div class="card-header">
            <span>租赁信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="押金类型">
            <el-tag :type="orderData.depositType === 'alipay_free' ? 'success' : 'danger'">
              {{ orderData.depositType === 'alipay_free' ? '支付宝免押' : '交押金' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="押金金额">¥{{ orderData.depositAmount || '--' }}</el-descriptions-item>
          <el-descriptions-item label="租赁周期">
            <el-tag :type="getRentModeType(orderData.rentMode)">
              {{ getRentModeText(orderData.rentMode) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="租赁开始时间">{{ formatDate(orderData.rentStartTime) }}</el-descriptions-item>
          <el-descriptions-item label="租赁时长">{{ orderData.rentDuration }}天</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 分期信息卡片 -->
      <el-card class="detail-card" shadow="hover" v-if="orderData.purchasePlan && orderData.purchasePlan !== 'full'">
        <template #header>
          <div class="card-header">
            <span>分期信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="分期方案">
            <el-tag :type="getPurchasePlanType(orderData.purchasePlan)">
              {{ getPurchasePlanText(orderData.purchasePlan) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分期手续费">¥{{ orderData.installmentFee || '--' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 收车信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>收车信息</span>
            <el-button v-if="orderData.status === 'pending'" type="primary" link @click="openAddressSelector">
              编辑
            </el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="取车方式">
            <el-tag :type="orderData.pickupMethod === 'to_store' ? 'success' : 'danger'">
              {{ orderData.pickupMethod === 'to_store' ? '到店取车' : '送车上门' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="收车地址">{{ getFullAddress }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ orderData.userName }}</el-descriptions-item>
          <el-descriptions-item label="用户手机">{{ orderData.userPhone }}</el-descriptions-item>
        </el-descriptions>
        <!-- 编辑表单 -->
        <AddressSelector v-model="addressInfo" ref="addressSelectorRef" @select="selectAddress" />
      </el-card>

      <!-- 车辆信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>车辆信息</span>
            <el-button v-if="orderData.status === 'pending' && !isEditVehicle" type="primary" link @click="editVehicleInfo">
              编辑
            </el-button>
            <el-button type="primary" link @click="saveVehicleInfo" v-if="orderData.status === 'pending' && isEditVehicle">保存</el-button>
          </div>
        </template>
        <el-descriptions :column="3" border v-if="isEditVehicle">
          <el-descriptions-item label="车辆名称">
            <el-input v-model="orderData.vehicleName" />
          </el-descriptions-item>
          <el-descriptions-item label="车架号">
            <el-input v-model="orderData.vehicleVin" />
          </el-descriptions-item>
          <el-descriptions-item label="车牌号">
            <el-input v-model="orderData.licensePlate" />
          </el-descriptions-item>
          <el-descriptions-item label="车辆图片">
            <el-input v-model="orderData.vehicleImage" placeholder="请输入车辆图片URL" />
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions :column="3" border v-else>
          <el-descriptions-item label="车辆名称">{{ orderData.vehicleName }}</el-descriptions-item>
          <el-descriptions-item label="车架号">{{ orderData.vehicleVin }}</el-descriptions-item>
          <el-descriptions-item label="车牌号">{{ orderData.licensePlate }}</el-descriptions-item>
          <el-descriptions-item label="车辆图片">
            <div v-if="orderData.vehicleImage" class="vehicle-image-container">
              <el-image
                :src="orderData.vehicleImage"
                :preview-src-list="[orderData.vehicleImage]"
                fit="cover"
                class="vehicle-image"
                :preview-teleported="true"
              />
            </div>
            <span v-else class="no-image">暂无图片</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 门店信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>门店信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="门店名称">{{ orderData.storeName }}</el-descriptions-item>
          <el-descriptions-item label="门店地址">{{ storeInfo.address || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ storeInfo.phone || '--' }}</el-descriptions-item>
          <el-descriptions-item label="营业时间">{{ storeInfo.businessHours || '--' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 编辑弹窗 -->
    <OrderForm ref="formRef" @success="handleEditSuccess" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ArrowLeftBold } from '@element-plus/icons-vue'
import { OrderApi, OrderVO } from '@/api/rental/order'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { MemberAddressApi, MemberAddressVO } from '@/api/rental/memberaddress'
import OrderForm from './OrderForm.vue'

/** 订单详情页面 */
defineOptions({ name: 'OrderDetail' })

const route = useRoute()
const router = useRouter()
const message = useMessage()
const { t } = useI18n()

const orderId = ref(route.params.id as string)
const orderData = ref<OrderVO>({} as OrderVO)
const storeInfo = ref<StoreVO>({} as StoreVO)
const addressInfo = ref<MemberAddressVO>({} as MemberAddressVO)
const loading = ref(true)
const statusLoading = ref(false)

// 是否修改收车地址
let isEditAddress = ref(false)
const addressSelectorRef = ref()
const openAddressSelector = () => {
  addressSelectorRef.value.open()
}

// 是否修改车辆信息
let isEditVehicle = ref(false)

// 返回完整地址（计算属性）
const getFullAddress = computed(() => {
  return addressInfo.value.province + addressInfo.value.city + addressInfo.value.district + addressInfo.value.detailAddress
})

// 获取订单详情
const getOrderDetail = async () => {
  loading.value = true
  try {
    orderData.value = await OrderApi.getOrder(Number(orderId.value))
    // 获取门店信息
    if (orderData.value.storeId) {
      storeInfo.value = await StoreApi.getStore(orderData.value.storeId)
    }
    // 获取地址信息
    if (orderData.value.addressId) {
      addressInfo.value = await MemberAddressApi.getMemberAddress(orderData.value.addressId)
    }
  } catch (error) {
    message.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'warning',
    'paid': 'success',
    'cancelled': 'danger',
    'completed': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待支付',
    'paid': '已支付',
    'cancelled': '已取消',
    'completed': '已完成'
  }
  return statusMap[status] || '未知'
}

const getRentModeType = (mode: string) => {
  const modeMap: Record<string, string> = {
    'day': 'success',
    'week': 'warning',
    'month': 'danger'
  }
  return modeMap[mode] || 'info'
}

const getRentModeText = (mode: string) => {
  const modeMap: Record<string, string> = {
    'day': '日租',
    'week': '周租',
    'month': '月租'
  }
  return modeMap[mode] || '未知'
}

const getPurchasePlanType = (plan: string) => {
  const planMap: Record<string, string> = {
    'full': 'success',
    '3': 'warning',
    '6': 'danger',
    '12': 'info'
  }
  return planMap[plan] || 'info'
}

const getPurchasePlanText = (plan: string) => {
  const planMap: Record<string, string> = {
    'full': '全款',
    '3': '分三期',
    '6': '分六期',
    '12': '分十二期'
  }
  return planMap[plan] || '未知'
}

const formatDate = (dateValue: any) => {
  if (!dateValue) return '--'
  const d = new Date(dateValue);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  const second = String(d.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

const canChangeStatus = computed(() => {
  return ['pending', 'paid'].includes(orderData.value.status)
})

// 编辑相关
const formRef = ref()


const editVehicleInfo = () => {
  isEditVehicle.value = true
}

const handleEditSuccess = () => {
  getOrderDetail()
  message.success('编辑成功')
}

// 状态切换
const handleStatusChange = async () => {
  try {
    statusLoading.value = true
    const newStatus = orderData.value.status === 'pending' ? 'paid' : 'completed'
    await OrderApi.updateOrder({
      ...orderData.value,
      status: newStatus
    })
    message.success('状态切换成功')
    await getOrderDetail()
  } catch (error) {
    message.error('状态切换失败')
  } finally {
    statusLoading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.go(-1)
}

const selectAddress = (address: any) => {
  addressInfo.value = address
  orderData.value.addressId = address.id
  updateOrderInfo()
  isEditAddress.value = false
}

const saveVehicleInfo = async () => {
  await OrderApi.updateOrder(orderData.value)
  await getOrderDetail()
  message.success('保存成功')
  isEditVehicle.value = false
}

// 更新订单信息
const updateOrderInfo = async () => {
  await OrderApi.updateOrder(orderData.value)
  await getOrderDetail()
  message.success('更新成功')
}

// 初始化
onMounted(() => {
  getOrderDetail()
})
</script>

<style scoped>
.order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.status-tag {
  font-size: 14px;
  padding: 6px 12px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-weight: 600;
  color: #303133;
}

.vehicle-image-container {
  display: flex;
  align-items: center;
}

.vehicle-image {
  width: 100px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}

.no-image {
  color: #909399;
  font-style: italic;
}
</style>