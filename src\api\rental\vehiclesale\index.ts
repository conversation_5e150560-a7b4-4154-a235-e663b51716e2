import request from '@/config/axios'

// 销售车辆 VO
export interface VehicleSaleVO {
  id: number // 主键ID
  vehicleNo: string // 车辆编号（平台内部唯一标识）
  licensePlate: string // 车牌号
  vin: string // 车架号（VIN）
  storeId: number // 所属门店ID（逻辑关联 rental_store.id）
  brandId: number // 品牌ID
  model: string // 型号
  detail: string // 车辆详细说明（如颜色、配置等）
  price: number // 销售价格（单位：元）
  images: string // 车辆图片，JSON数组格式（["url1", "url2", ...]）
  name: string // 名称
  status: boolean // 状态（1=上架，0=下架）
}

// 销售车辆 API
export const VehicleSaleApi = {
  // 查询销售车辆分页
  getVehicleSalePage: async (params: any) => {
    return await request.get({ url: `/rental/vehicle-sale/page`, params })
  },

  // 查询销售车辆详情
  getVehicleSale: async (id: number) => {
    return await request.get({ url: `/rental/vehicle-sale/get?id=` + id })
  },

  // 新增销售车辆
  createVehicleSale: async (data: VehicleSaleVO) => {
    return await request.post({ url: `/rental/vehicle-sale/create`, data })
  },

  // 修改销售车辆
  updateVehicleSale: async (data: VehicleSaleVO) => {
    return await request.put({ url: `/rental/vehicle-sale/update`, data })
  },

  // 删除销售车辆
  deleteVehicleSale: async (id: number) => {
    return await request.delete({ url: `/rental/vehicle-sale/delete?id=` + id })
  },

  // 导出销售车辆 Excel
  exportVehicleSale: async (params) => {
    return await request.download({ url: `/rental/vehicle-sale/export-excel`, params })
  },

  // 划拨
  transferVehicleSale: async (id: number, targetStoreId: number) => {
    return await request.get({ url: `/rental/vehicle-sale/transfer?id=${id}&storeId=${targetStoreId}` })
  }
}
