import request from '@/config/axios'

// 门店下单参数配置 VO
export interface StoreConfigVO {
  id: number // 主键ID
  storeId: number // 门店ID
  minAge: number // 最小下单年龄
  maxAge: number // 最大下单年龄
  banOtherRent: boolean // 禁止其他门店在租用户
  banOtherResv: boolean // 禁止其他门店预约用户
  banSelfRent: boolean // 禁止本门店在租用户
  banSelfResv: boolean // 禁止本门店预约用户
  banOtherBlack: boolean // 禁止其他门店黑名单用户
  isDeleted: boolean // 逻辑删除标识
}

// 门店下单参数配置 API
export const StoreConfigApi = {
  // 查询门店下单参数配置分页
  getStoreConfigPage: async (params: any) => {
    return await request.get({ url: `/rental/store-config/page`, params })
  },

  // 查询门店下单参数配置详情
  getStoreConfig: async (id: number) => {
    return await request.get({ url: `/rental/store-config/get?id=` + id })
  },

  // 新增门店下单参数配置
  createStoreConfig: async (data: StoreConfigVO) => {
    return await request.post({ url: `/rental/store-config/create`, data })
  },

  // 修改门店下单参数配置
  updateStoreConfig: async (data: StoreConfigVO) => {
    return await request.put({ url: `/rental/store-config/update`, data })
  },

  // 删除门店下单参数配置
  deleteStoreConfig: async (id: number) => {
    return await request.delete({ url: `/rental/store-config/delete?id=` + id })
  },

  // 导出门店下单参数配置 Excel
  exportStoreConfig: async (params) => {
    return await request.download({ url: `/rental/store-config/export-excel`, params })
  },
}