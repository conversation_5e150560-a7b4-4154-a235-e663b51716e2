<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="180px"
      v-loading="formLoading"
    >
      <el-form-item label="门店" prop="storeId">
        <el-select v-model="formData.storeId" placeholder="请选择门店">
          <el-option
            v-for="item in storeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最小下单年龄" prop="minAge">
        <el-input-number v-model="formData.minAge" :min="16" :max="100" :step="1" />
      </el-form-item>
      <el-form-item label="最大下单年龄" prop="maxAge">
        <el-input-number v-model="formData.maxAge" :min="16" :max="100" :step="1" />
      </el-form-item>
      <el-form-item label="禁止其他门店在租用户" prop="banOtherRent">
        <el-switch
          v-model="formData.banOtherRent"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
      <!-- <el-form-item label="禁止其他门店预约用户" prop="banOtherResv">
        <el-switch
          v-model="formData.banOtherResv"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item> -->
      <el-form-item label="禁止本门店在租用户" prop="banSelfRent">
        <el-switch
          v-model="formData.banSelfRent"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
      <!-- <el-form-item label="禁止本门店预约用户" prop="banSelfResv">
        <el-switch
          v-model="formData.banSelfResv"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item> -->
      <el-form-item label="禁止门店黑名单用户" prop="banOtherBlack">
        <el-switch
          v-model="formData.banOtherBlack"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { StoreConfigApi, StoreConfigVO } from '@/api/rental/storeconfig'
import { StoreApi, StoreVO } from '@/api/rental/store'

/** 门店下单参数配置 表单 */
defineOptions({ name: 'StoreConfigForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  storeId: undefined,
  minAge: undefined,
  maxAge: undefined,
  banOtherRent: undefined,
  banOtherResv: undefined,
  banSelfRent: undefined,
  banSelfResv: undefined,
  banOtherBlack: undefined
})
const formRules = reactive({
  storeId: [{ required: true, message: '门店ID不能为空', trigger: 'blur' }],
  isDeleted: [{ required: true, message: '逻辑删除标识不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await StoreConfigApi.getStoreConfig(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as StoreConfigVO
    if (formType.value === 'create') {
      await StoreConfigApi.createStoreConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await StoreConfigApi.updateStoreConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 获取所有的门店
const storeList = ref<StoreVO[]>([])
const getStoreList = async () => {
  storeList.value = await StoreApi.getStoreList()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    storeId: undefined,
    minAge: undefined,
    maxAge: undefined,
    banOtherRent: false,
    banOtherResv: false,
    banSelfRent: false,
    banSelfResv: false,
    banOtherBlack: false
  }
  formRef.value?.resetFields()
}

/** 初始化 **/
onMounted(() => {
  getStoreList()
})
</script>