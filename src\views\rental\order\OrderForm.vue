<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item label="订单编号" prop="orderNo">
        <el-input v-model="formData.orderNo" placeholder="请输入订单编号" />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-radio-group v-model="formData.orderType" @change="getVehicleList">
          <el-radio-button label="rent">租车</el-radio-button>
          <el-radio-button label="buy">购车</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="门店" prop="storeId">
        <el-select v-model="formData.storeId" placeholder="请选择门店">
          <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆" prop="vehicleId">
        <el-select v-model="formData.vehicleId" placeholder="请选择车辆">
          <el-option v-for="item in vehicleList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="押金类型" prop="depositType">
        <el-radio-group v-model="formData.depositType">
          <el-radio-button label="normal">交押金</el-radio-button>
          <el-radio-button label="alipay_free">支付宝免押</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="押金金额" prop="depositAmount"
        v-if="formData.orderType === 'rent' && formData.depositType === 'normal'">
        <el-input v-model="formData.depositAmount" placeholder="请输入押金金额" />
      </el-form-item>
      <el-form-item label="租赁周期" prop="rentMode" v-if="formData.orderType === 'rent'">
        <el-radio-group v-model="formData.rentMode">
          <el-radio label="day">日租</el-radio>
          <el-radio label="week">周租</el-radio>
          <el-radio label="month">月租</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="支付方式" prop="rentPaymentMode">
        <el-radio-group v-model="formData.rentPaymentMode">
          <el-radio-button label="prepay">先付后用</el-radio-button>
          <el-radio-button label="postpay">先用后付</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="租赁开始时间" prop="rentStartTime" v-if="formData.orderType === 'rent'">
        <el-date-picker v-model="formData.rentStartTime" type="date" value-format="x" placeholder="选择租赁开始时间" />
      </el-form-item>
      <el-form-item label="租赁时长" prop="rentDuration" v-if="formData.orderType === 'rent'">
        <el-input v-model="formData.rentDuration" placeholder="请输入租赁时长" />
      </el-form-item>
      <el-form-item label="分期方案" prop="purchasePlan">
        <el-radio-group v-model="formData.purchasePlan">
          <el-radio-button label="full">全款</el-radio-button>
          <el-radio-button label="3">分三期</el-radio-button>
          <el-radio-button label="6">分六期</el-radio-button>
          <el-radio-button label="12">分十二期</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分期手续费" prop="installmentFee" v-if="formData.purchasePlan !== 'full'">
        <el-input v-model="formData.installmentFee" placeholder="请输入分期手续费" />
      </el-form-item>
      <el-form-item label="订单金额" prop="amount">
        <el-input v-model="formData.amount" placeholder="请输入订单金额" />
      </el-form-item>
      <el-form-item label="支付方式" prop="payType">
        <el-select v-model="formData.payType" placeholder="请选择支付方式">
          <el-option label="支付宝" value="alipay" />
          <el-option label="微信" value="wechat" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付宝交易凭证号" prop="alipayTradeNo" label-width="130">
        <el-input v-model="formData.alipayTradeNo" placeholder="请输入支付宝交易凭证号" />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择订单状态">
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="已取消" value="canceled" />
          <el-option label="已完成" value="completed" />
        </el-select>
      </el-form-item>
      <el-form-item label="车架号" prop="vehicleVin">
        <el-input v-model="formData.vehicleVin" placeholder="请输入车架号" />
      </el-form-item>
      <el-form-item label="车牌" prop="licensePlate">
        <el-input v-model="formData.licensePlate" placeholder="请输入车牌" />
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="formData.userName" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="用户手机" prop="userPhone">
        <el-input v-model="formData.userPhone" placeholder="请输入用户手机" />
      </el-form-item>
      <el-form-item label="取车方式" prop="pickupMethod">
        <el-radio-group v-model="formData.pickupMethod">
          <el-radio-button label="to_store">到店取车</el-radio-button>
          <el-radio-button label="deliver">送车上门</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="收车地址" prop="addressId" v-if="formData.pickupMethod === 'deliver'">
        <el-input v-model="formData.fullAddress" placeholder="请选择收车地址" readonly>
          <template #append>
            <div @click="openAddressSelector" style="cursor: pointer;">
              <span>切换</span>
            </div>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
  <AddressSelector v-model="formData.addressId" ref="addressSelectorRef" @select="selectAddress" />
</template>
<script setup lang="ts">
import { OrderApi, OrderVO } from '@/api/rental/order'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { VehicleRentApi, VehicleRentVO } from '@/api/rental/vehiclerent'
import { VehicleSaleApi, VehicleSaleVO } from '@/api/rental/vehiclesale'
import AddressSelector from '@/components/AddressSelector/index.vue'

/** 租车/购车统一订单 表单 */
defineOptions({ name: 'OrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  orderNo: undefined,
  userId: undefined,
  orderType: undefined,
  storeId: undefined,
  vehicleId: undefined,
  depositType: undefined,
  depositAmount: undefined,
  rentMode: undefined,
  rentPaymentMode: undefined,
  rentStartTime: undefined,
  rentDuration: undefined,
  purchasePlan: undefined,
  installmentFee: undefined,
  amount: undefined,
  payType: undefined,
  alipayTradeNo: undefined,
  status: undefined,
  isDeleted: undefined,
  storeName: undefined,
  vehicleName: undefined,
  vehicleVin: undefined,
  licensePlate: undefined,
  userName: undefined,
  userPhone: undefined,
  pickupMethod: undefined,
  addressId: undefined,
  fullAddress: undefined,
})
const formRules = reactive({
  orderNo: [{ required: true, message: '订单编号不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  orderType: [{ required: true, message: '订单类型：rent=租车，buy=购车不能为空', trigger: 'change' }],
  storeId: [{ required: true, message: '门店ID不能为空', trigger: 'blur' }],
  vehicleId: [{ required: true, message: '车辆ID不能为空', trigger: 'blur' }],
  amount: [{ required: true, message: '订单金额（不含押金）不能为空', trigger: 'blur' }],
  isDeleted: [{ required: true, message: '是否逻辑删除不能为空', trigger: 'blur' }],
  storeName: [{ required: true, message: '门店名称不能为空', trigger: 'blur' }],
  vehicleName: [{ required: true, message: '租车名称不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const addressSelectorRef = ref() // 地址选择器 Ref
// 获取所有的门店
const storeList = ref<StoreVO[]>([])
const getStoreList = async () => {
  storeList.value = await StoreApi.getStoreList()
}
const vehicleList = ref<VehicleRentVO[] | VehicleSaleVO[]>([])
// 根据订单类型获取车辆列表
const getVehicleList = async () => {
  const params = {
    pageNo: 1,
    pageSize: 100
  }
  if (formData.value.orderType === 'rent') {
    const res = await VehicleRentApi.getVehicleRentPage(params)
    vehicleList.value = res.list
  } else {
    const res = await VehicleSaleApi.getVehicleSalePage(params)
    vehicleList.value = res.list
  }
}
const openAddressSelector = () => {
  addressSelectorRef.value.open()
}
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderApi.getOrder(id)
      console.log('订单', formData.value);
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderVO
    if (formType.value === 'create') {
      await OrderApi.createOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderApi.updateOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    orderNo: undefined,
    userId: undefined,
    orderType: undefined,
    storeId: undefined,
    vehicleId: undefined,
    depositType: undefined,
    depositAmount: undefined,
    rentMode: undefined,
    rentPaymentMode: undefined,
    rentStartTime: undefined,
    rentDuration: undefined,
    purchasePlan: undefined,
    installmentFee: undefined,
    amount: undefined,
    payType: undefined,
    alipayTradeNo: undefined,
    status: undefined,
    isDeleted: undefined,
    storeName: undefined,
    vehicleName: undefined,
    vehicleVin: undefined,
    licensePlate: undefined,
    userName: undefined,
    userPhone: undefined,
    pickupMethod: undefined,
    addressId: undefined,
  }
  formRef.value?.resetFields()
}

/* 选择地址 */
const selectAddress = (address: any) => {
  console.log('选择地址', address);
  formData.value.addressId = address.id
  formData.value.fullAddress = address.fullAddress
}
onMounted(() => {
  getStoreList()
  getVehicleList()
})
</script>