import request from '@/config/axios'

// 基本数据统计 VO
export interface StatisticsRespVO {
  orderCount: number // 订单总数
  vehicleCount: number // 车辆总数（包括租车和购车）
  storeCount: number // 门店数量
  monthlyRevenue: number // 本月营收（元）
}

// 会员统计数据 VO
export interface MemberStatisticsRespVO {
  totalCount: number // 会员总数
  todayNewCount: number // 今日新增会员数
  monthActiveCount: number // 月活跃会员数
  conversionRate: number // 转化率（实名认证会员数/总会员数）
}

// 租车状态统计数据 VO
export interface RentVehicleStatusRespVO {
  onShelfCount: number // 上架车辆数量
  offShelfCount: number // 下架车辆数量
  soldCount: number // 已出售车辆数量
}

// 近期订单趋势统计数据 VO
export interface OrderTrendRespVO {
  dateList: string[] // 日期列表，格式为MM-dd
  rentOrderCounts: number[] // 租车订单数量列表
  buyOrderCounts: number[] // 购车订单数量列表
}

// 订单状态统计数据 VO
export interface OrderStatusStatisticsRespVO {
  pendingCount: number // 待支付订单数量
  paidCount: number // 已支付订单数量
  inProgressCount: number // 进行中订单数量
  completedCount: number // 已完成订单数量
  cancelledCount: number // 已取消订单数量
}

// 门店营收统计数据 VO
export interface StoreRevenueRespVO {
  storeId: number // 门店ID
  storeName: string // 门店名称
  amount: number // 营收金额
}

// 最新订单数据 VO
export interface LatestOrderRespVO {
  id: number // 订单ID
  orderNo: string // 订单编号
  userName: string // 客户姓名
  orderType: string // 订单类型：rent=租车，buy=购车
  vehicleName: string // 车辆名称
  createTime: number // 创建时间（时间戳）
  status: string // 订单状态
}

// 首页 API
export const HomeApi = {
  // 获取基本数据统计
  getStatistics: async (): Promise<StatisticsRespVO> => {
    return await request.get({ url: `/rental/home-index/statistics` })
  },

  // 获取会员统计数据
  getMemberStatistics: async (): Promise<MemberStatisticsRespVO> => {
    return await request.get({ url: `/rental/home-index/member-statistics` })
  },

  // 获取租车状态统计数据
  getRentVehicleStatus: async (): Promise<RentVehicleStatusRespVO> => {
    return await request.get({ url: `/rental/home-index/rent-vehicle-status` })
  },

  // 获取近期订单趋势统计数据
  getOrderTrend: async (): Promise<OrderTrendRespVO> => {
    return await request.get({ url: `/rental/home-index/order-trend` })
  },

  // 获取订单状态统计数据
  getOrderStatus: async (): Promise<OrderStatusStatisticsRespVO> => {
    return await request.get({ url: `/rental/home-index/order-status` })
  },

  // 获取门店营收统计数据
  getStoreRevenue: async (): Promise<StoreRevenueRespVO[]> => {
    return await request.get({ url: `/rental/home-index/store-revenue` })
  },

  // 获取最新订单数据
  getLatestOrders: async (): Promise<LatestOrderRespVO[]> => {
    return await request.get({ url: `/rental/home-index/latest-orders` })
  }
}