<template>
  <Dialog v-model="dialogVisible" title="选择用户" width="900px" :close-on-click-modal="false">
    <!-- 搜索表单 -->
    <el-form
      ref="queryFormRef"
      :model="queryParams"
      :inline="true"
      label-width="68px"
      class="-mb-15px"
    >
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item style="margin-left: auto;">
        <el-button @click="handleQuery" type="primary" size="default">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery" size="default">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 用户表格 -->
    <el-table
      v-loading="loading"
      :data="list"
      highlight-current-row
      @current-change="handleCurrentChange"
      @row-dblclick="selectUser"
      style="width: 100%;margin-top: 10px;"
      class="user-table"
      stripe
      border
      :height="400"
    >
      <template #empty>
        <el-empty description="暂无用户数据" :image-size="80" />
      </template>
      <el-table-column prop="nickname" label="用户昵称"/>
      <el-table-column prop="phoneNumber" label="手机号" width="150" />
      <el-table-column prop="gender" label="性别" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_MEMBER_SEX" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_MEMBER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
       <el-table-column label="是否实名" align="center" prop="identity">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.identity">是</el-tag>
          <el-tag type="info" v-else>否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" :formatter="dateFormatter" />
      <el-table-column label="操作" width="100" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="selectUser(scope.row)"
            :icon="Check"
          >
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <div style="height: 50px;"></div>
  </Dialog>
</template>

<script setup lang="ts">
import { MemberApi, MemberVO } from '@/api/rental/member'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { Check, Close } from '@element-plus/icons-vue'

/** 用户选择弹窗 */
defineOptions({ name: 'UserSelectDialog' })

const emit = defineEmits(['confirm'])

const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(true) // 列表的加载中
const list = ref<MemberVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedUser = ref<MemberVO | null>(null) // 选中的用户

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  phoneNumber: undefined,
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MemberApi.getMemberPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 处理当前行变化 */
const handleCurrentChange = (currentRow: MemberVO | null) => {
  selectedUser.value = currentRow
}

/** 选择用户 */
const selectUser = (user: MemberVO) => {
  selectedUser.value = user
  confirmSelect()
}

/** 确认选择 */
const confirmSelect = () => {
  if (selectedUser.value) {
    emit('confirm', selectedUser.value)
    dialogVisible.value = false
  }
}

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  selectedUser.value = null
  resetQuery()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>