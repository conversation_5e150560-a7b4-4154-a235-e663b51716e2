<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item label="车辆编号" prop="vehicleNo">
        <el-input v-model="formData.vehicleNo" placeholder="请输入车辆编号" :readonly="formType === 'create'" />
      </el-form-item>
            <el-form-item label="车辆名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入车辆名称" />
      </el-form-item>
      <el-form-item label="车辆图片" prop="images">
        <UploadImg v-model="formData.images" />
      </el-form-item>
      <el-form-item label="车牌号" prop="licensePlate">
        <el-input v-model="formData.licensePlate" placeholder="请输入车牌号" />
      </el-form-item>
      <el-form-item label="车架号" prop="vin">
        <el-input v-model="formData.vin" placeholder="请输入车架号（VIN）" />
      </el-form-item>
      <el-form-item label="门店" prop="storeId">
        <el-select v-model="formData.storeId" placeholder="请选择门店">
          <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="品牌" prop="brandId">
        <el-select v-model="formData.brandId" placeholder="请选择品牌">
          <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="型号" prop="model">
        <el-input v-model="formData.model" placeholder="请输入型号" />
      </el-form-item>
      <el-form-item label="销售价格" prop="price">
        <el-input v-model="formData.price" placeholder="请输入销售价格（单位：元）" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
                  <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_PURCHASE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆说明" prop="detail">
        <Editor v-model="formData.detail" height="300px" />
      </el-form-item>
      
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { VehicleSaleApi, VehicleSaleVO } from '@/api/rental/vehiclesale'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { BrandApi, BrandVO } from '@/api/rental/brand'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { formatTime } from '@/utils'

/** 销售车辆 表单 */
defineOptions({ name: 'VehicleSaleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined as number | undefined,
  vehicleNo: undefined as string | undefined,
  licensePlate: undefined as string | undefined,
  vin: undefined as string | undefined,
  storeId: undefined as number | undefined,
  brandId: undefined as number | undefined,
  model: undefined as string | undefined,
  detail: undefined as string | undefined,
  price: undefined as number | undefined,
  images: undefined as string | undefined,
  name: undefined as string | undefined,
  status: undefined as boolean | undefined,
})
const formRules = reactive({
  vehicleNo: [{ required: true, message: '车辆编号不能为空', trigger: 'blur' }],
  storeId: [{ required: true, message: '所属门店不能为空', trigger: 'blur' }],
  brandId: [{ required: true, message: '品牌不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '销售价格不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 新增时，自动生成车辆编号
  if (type === 'create') {
    // 生成格式为B+年月日时分秒的编号
    const now = new Date()
    formData.value.vehicleNo = 'B' + formatTime(now, 'yyyyMMddHHmmss')
  }
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await VehicleSaleApi.getVehicleSale(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as VehicleSaleVO
    if (formType.value === 'create') {
      await VehicleSaleApi.createVehicleSale(data)
      message.success(t('common.createSuccess'))
    } else {
      await VehicleSaleApi.updateVehicleSale(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined as number | undefined,
    vehicleNo: undefined as string | undefined,
    licensePlate: undefined as string | undefined,
    vin: undefined as string | undefined,
    storeId: undefined as number | undefined,
    brandId: undefined as number | undefined,
    model: undefined as string | undefined,
    detail: undefined as string | undefined,
    price: undefined as number | undefined,
    images: undefined as string | undefined,
    name: undefined as string | undefined,
    status: undefined as boolean | undefined,
  }
  formRef.value?.resetFields()
}

// 获取所有的门店
const storeList = ref<StoreVO[]>([])
const getStoreList = async () => {
  storeList.value = await StoreApi.getStoreList()
}

// 获取所有品牌
const brandList = ref<BrandVO[]>([])
const getBrandList = async () => {
  brandList.value = await BrandApi.getBrandList()
}

/** 初始化 **/
onMounted(() => {
  getStoreList()
  getBrandList()
})
</script>