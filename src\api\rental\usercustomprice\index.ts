import request from '@/config/axios'

// 租赁模式 VO
export interface UserCustomPriceVO {
  id: number // 主键ID
  userId: number // 用户ID
  mode: string // 租赁模式（hour=时租，day=日租，month=月租）
  enabled: boolean // 是否启用（1=启用，0=禁用）
  expireTime: Date // 授权过期时间
  remark: string // 备注
}

// 租赁模式 API
export const UserCustomPriceApi = {
  // 查询租赁模式分页
  getUserCustomPricePage: async (params: any) => {
    return await request.get({ url: `/rental/user-custom-price/page`, params })
  },

  // 查询租赁模式详情
  getUserCustomPrice: async (id: number) => {
    return await request.get({ url: `/rental/user-custom-price/get?id=` + id })
  },

  // 新增租赁模式
  createUserCustomPrice: async (data: UserCustomPriceVO) => {
    return await request.post({ url: `/rental/user-custom-price/create`, data })
  },

  // 修改租赁模式
  updateUserCustomPrice: async (data: UserCustomPriceVO) => {
    return await request.put({ url: `/rental/user-custom-price/update`, data })
  },

  // 删除租赁模式
  deleteUserCustomPrice: async (id: number) => {
    return await request.delete({ url: `/rental/user-custom-price/delete?id=` + id })
  },

  // 导出租赁模式 Excel
  exportUserCustomPrice: async (params) => {
    return await request.download({ url: `/rental/user-custom-price/export-excel`, params })
  },
}