<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户" prop="userId">
        <el-select v-model="formData.userId" placeholder="请选择用户" filterable clearable>
          <el-option
            v-for="item in memberList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="拉黑原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          placeholder="请输入拉黑原因"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="到期时间" >
        <el-date-picker
          v-model="formData.expireTime"
          type="datetime"
          placeholder="请选择到期时间"
          value-format="x"
          :disabledDate="disabledDate"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { StoreBlacklistApi, StoreBlacklistVO } from '@/api/rental/storeblacklist'
import { MemberApi } from '@/api/rental/member'


/** 门店用户黑名单 表单 */
defineOptions({ name: 'StoreBlacklistForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 接收父组件传递的门店ID
const props = defineProps({
  storeId: {
    type: Number,
    required: true
  }
})

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const memberList = ref([]) // 用户列表

const formData = ref<StoreBlacklistVO>({
  id: undefined,
  storeId: undefined,
  userId: undefined,
  reason: '',
  expireTime: undefined
})
const formRules = reactive({
  userId: [{ required: true, message: '用户不能为空', trigger: 'change' }],
  reason: [{ required: true, message: '拉黑原因不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref


/** 禁用当前日期之前的日期 */
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天之前的日期
}

/** 获取用户列表 */
const getMemberList = async () => {
  try {
    const res = await MemberApi.getMemberList()
    memberList.value = res
  } catch (error) {
    console.error('获取用户列表失败', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 设置门店ID
  formData.value.storeId = props.storeId
  
  // 加载用户列表
  await getMemberList()
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await StoreBlacklistApi.getStoreBlacklist(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    storeId: props.storeId,
    userId: undefined,
    reason: '',
    expireTime: undefined
  }
  formRef.value?.resetFields()
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 提交请求
  formLoading.value = true
  try {
    // 判断是修改还是新增
    const data = formType.value === 'create' 
      ? await StoreBlacklistApi.createStoreBlacklist(formData.value)
      : await StoreBlacklistApi.updateStoreBlacklist(formData.value)
    
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    // 处理错误情况
    console.error(error)
  } finally {
    formLoading.value = false
  }
}
</script> 