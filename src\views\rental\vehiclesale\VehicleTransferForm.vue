<template>
  <el-dialog
    :title="'车辆划拨'"
    v-model="dialogVisible"
    width="500px"
    destroy-on-close
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="车辆名称">
        <el-input v-model="vehicleInfo.name" disabled />
      </el-form-item>
      <el-form-item label="车辆编号">
        <el-input v-model="vehicleInfo.vehicleNo" disabled />
      </el-form-item>
      <el-form-item label="当前门店">
        <el-input v-model="currentStoreName" disabled />
      </el-form-item>
      <el-form-item label="目标门店" prop="targetStoreId">
        <el-select
          v-model="formData.targetStoreId"
          placeholder="请选择目标门店"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in filteredStoreList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm" :disabled="formLoading">
        确 定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { VehicleSaleApi, VehicleSaleVO } from '@/api/rental/vehiclesale'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { useMessage } from '@/hooks/web/useMessage'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const vehicleId = ref<number | undefined>() // 车辆ID
const vehicleInfo = ref<Partial<VehicleSaleVO>>({}) // 车辆信息
const currentStoreName = ref('') // 当前门店名称

// 表单数据
const formData = reactive({
  targetStoreId: undefined, // 目标门店ID
})

// 表单校验规则
const formRules = reactive({
  targetStoreId: [{ required: true, message: '请选择目标门店', trigger: 'change' }],
})

// 门店列表
const storeList = ref<StoreVO[]>([])
// 过滤后的门店列表（排除当前门店）
const filteredStoreList = computed(() => {
  return storeList.value.filter((store) => store.id !== vehicleInfo.value.storeId)
})

// 获取所有门店
const getStoreList = async () => {
  storeList.value = await StoreApi.getStoreList()
}

// 获取车辆详情
const getVehicleInfo = async (id: number) => {
  try {
    formLoading.value = true
    const data = await VehicleSaleApi.getVehicleSale(id)
    vehicleInfo.value = data
    
    // 获取当前门店名称
    if (vehicleInfo.value.storeId && storeList.value.length > 0) {
      const currentStore = storeList.value.find(store => store.id === vehicleInfo.value.storeId)
      currentStoreName.value = currentStore ? currentStore.name : '未知门店'
    } else {
      currentStoreName.value = '未知门店'
    }
  } finally {
    formLoading.value = false
  }
}

// 打开弹窗
const open = async (id: number) => {
  resetForm()
  dialogVisible.value = true
  vehicleId.value = id
  await getStoreList()
  await getVehicleInfo(id)
}

// 提交表单
const emit = defineEmits(['success']) // 定义emit
const formRef = ref() // 表单 Ref
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交表单
  try {
    formLoading.value = true
    if (vehicleId.value === undefined) {
      throw new Error('车辆ID不能为空')
    }
    await VehicleSaleApi.transferVehicleSale(vehicleId.value, formData.targetStoreId)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 通知父组件刷新
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.targetStoreId = undefined
  vehicleInfo.value = {}
  currentStoreName.value = ''
  formLoading.value = false
  vehicleId.value = undefined
}

// 暴露方法
defineExpose({
  open
})
</script> 