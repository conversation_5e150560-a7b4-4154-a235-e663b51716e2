import request from '@/config/axios'

// 租车门店 VO
export interface StoreVO {
  id: number // 主键ID
  name: string // 门店名称
  address: string // 门店地址
  longitude: number // 经度
  latitude: number // 纬度
  businessHours: string // 营业时间（如 10:00-18:00）
  supportPickup: boolean // 是否支持到店取车（1=是, 0=否）
  supportDelivery: boolean // 是否支持送车上门（1=是, 0=否）
  phone: string // 联系电话
  status: boolean // 门店状态（1=营业中，0=已停业）
}

// 租车门店 API
export const StoreApi = {
  // 查询租车门店分页
  getStorePage: async (params: any) => {
    return await request.get({ url: `/rental/store/page`, params })
  },

  // 查询租车门店详情
  getStore: async (id: number) => {
    return await request.get({ url: `/rental/store/get?id=` + id })
  },

  // 新增租车门店
  createStore: async (data: StoreVO) => {
    return await request.post({ url: `/rental/store/create`, data })
  },

  // 修改租车门店
  updateStore: async (data: StoreVO) => {
    return await request.put({ url: `/rental/store/update`, data })
  },

  // 删除租车门店
  deleteStore: async (id: number) => {
    return await request.delete({ url: `/rental/store/delete?id=` + id })
  },

  // 导出租车门店 Excel
  exportStore: async (params) => {
    return await request.download({ url: `/rental/store/export-excel`, params })
  },

  // 生成租车门店二维码
  generateStoreQrCode: async (id: number) => {
    return await request.download({ url: `/rental/store/qrcode?storeId=` + id })
  },

  // 获取所有的门店
  getStoreList: async () => {
    return await request.get({ url: `/rental/store/get-all` })
  },

  // ==================== 子表（门店图片） ====================

  // 获得门店图片列表
  getStoreImageListByStoreId: async (storeId) => {
    return await request.get({ url: `/rental/store/store-image/list-by-store-id?storeId=` + storeId })
  },
}