<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item class="float-right">
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="primary" @click="handleBack">返回</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="用户昵称" align="center" prop="memberNickname" />
      <el-table-column label="用户手机" align="center" prop="memberPhone" />
      <el-table-column label="拉黑原因" align="center" prop="reason" />
      <el-table-column label="到期时间" align="center" prop="expireTime">
        <template #default="scope">
          <span v-if="scope.row.expireTime">
            {{ formatTime(scope.row.expireTime) }}
          </span>
          <span v-else>永久</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="100px">
        <template #default="scope">
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            解除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <StoreBlacklistForm ref="formRef" @success="getList" :storeId="storeId" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { StoreBlacklistApi, StoreBlacklistVO } from '@/api/rental/storeblacklist'
import StoreBlacklistForm from './StoreBlacklistForm.vue'
import dayjs from 'dayjs'

/** 门店用户黑名单 列表 */
defineOptions({ name: 'StoreBlacklist' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute() // 路由
const router = useRouter() // 路由器

const storeId = ref(Number(route.params.id)) // 获取路由中的门店ID
const loading = ref(true) // 列表的加载中
const list = ref<StoreBlacklistVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  storeId: undefined,
})
const queryFormRef = ref() // 搜索的表单

/** 格式化日期 */
const formatTime = (time: string | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!time) return ''
  return dayjs(time).format(format)
}


/** 查询列表 */
const getList = async () => {
  if (!storeId.value) return
  
  loading.value = true
  try {
    // 调用接口获取指定门店的黑名单列表
    const data = await StoreBlacklistApi.getStoreBlacklistPage({
      ...queryParams,
      storeId: storeId.value
    })
    list.value = data.list || []
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 返回按钮操作 */
const handleBack = () => {
  router.back()
}

/** 添加操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await StoreBlacklistApi.deleteStoreBlacklist(id)
    message.success('解除成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>