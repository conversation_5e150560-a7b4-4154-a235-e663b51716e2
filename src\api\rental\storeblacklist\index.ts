import request from '@/config/axios'

// 门店用户拉黑 VO
export interface StoreBlacklistVO {
  id: number // 主键ID
  storeId: number // 门店ID
  userId: number // 用户ID
  reason: string // 拉黑原因
  expireTime: Date // 到期时间
}

// 门店用户拉黑 API
export const StoreBlacklistApi = {
  // 查询门店用户拉黑分页
  getStoreBlacklistPage: async (params: any) => {
    return await request.get({ url: `/rental/store-blacklist/page`, params })
  },

  // 查询门店用户拉黑详情
  getStoreBlacklist: async (id: number) => {
    return await request.get({ url: `/rental/store-blacklist/get?id=` + id })
  },

  // 新增门店用户拉黑
  createStoreBlacklist: async (data: StoreBlacklistVO) => {
    return await request.post({ url: `/rental/store-blacklist/create`, data })
  },

  // 修改门店用户拉黑
  updateStoreBlacklist: async (data: StoreBlacklistVO) => {
    return await request.put({ url: `/rental/store-blacklist/update`, data })
  },

  // 删除门店用户拉黑
  deleteStoreBlacklist: async (id: number) => {
    return await request.delete({ url: `/rental/store-blacklist/delete?id=` + id })
  },

  // 导出门店用户拉黑
  exportStoreBlacklist: async (params: any) => {
    return await request.download({ url: `/rental/store-blacklist/export-excel`, params })
  },
}