<template>
  <div>
    <el-dialog v-model="dialogVisible" title="选择收车地址" width="800px">
      <div class="address-selector">
        <div class="search-area mb-4">
          <el-form :inline="true" :model="queryParams">
            <el-form-item label="联系人">
              <el-input v-model="queryParams.contactName" placeholder="请输入联系人姓名" clearable />
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input v-model="queryParams.contactPhone" placeholder="请输入联系电话" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="addressList" :stripe="true" :show-overflow-tooltip="true"
          @row-click="handleRowClick" highlight-current-row>
          <el-table-column label="序号" type="index" align="center" width="60" />
          <el-table-column label="联系人" align="center" prop="contactName" width="100" />
          <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
          <el-table-column label="省市区" align="center" width="200">
            <template #default="scope">
              {{ scope.row.province }} {{ scope.row.city }} {{ scope.row.district }}
            </template>
          </el-table-column>
          <el-table-column label="详细地址" align="center" prop="detailAddress" />
          <el-table-column label="默认地址" align="center" width="100">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.isDefault">是</el-tag>
              <el-tag type="info" v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="handleSelect(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-wrapper mt-4">
          <el-pagination v-model:current-page="queryParams.pageNo" v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { MemberAddressApi, MemberAddressVO } from '@/api/rental/memberaddress'

/** 地址选择器组件 */
defineOptions({ name: 'AddressSelector' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 定义事件
const emit = defineEmits(['select'])

// 弹窗控制
const dialogVisible = ref(false)
const loading = ref(false)

// 查询参数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  contactName: '',
  contactPhone: ''
})

// 数据列表
const addressList = ref<MemberAddressVO[]>([])
const total = ref(0)

// 打开弹窗
const open = () => {
  dialogVisible.value = true;
  console.log('打开地址选择器', dialogVisible.value);
  loadAddressList()
}

// 加载地址列表
const loadAddressList = async () => {
  loading.value = true
  try {
    const res = await MemberAddressApi.getMemberAddressPage(queryParams.value)
    addressList.value = res.list || []
    total.value = res.total || 0
  } catch (error) {
    console.error('加载地址列表失败:', error)
    message.error('加载地址列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.value.pageNo = 1
  loadAddressList()
}

// 重置
const handleReset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    contactName: '',
    contactPhone: ''
  }
  loadAddressList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.value.pageSize = size
  loadAddressList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.value.pageNo = page
  loadAddressList()
}

// 行点击
const handleRowClick = (row: MemberAddressVO) => {
  handleSelect(row)
}

// 选择地址
const handleSelect = (row: MemberAddressVO) => {
  // 构建完整的地址信息
  const addressInfo = {
    id: row.id,
    contactName: row.contactName,
    contactPhone: row.contactPhone,
    province: row.province,
    city: row.city,
    district: row.district,
    detailAddress: row.detailAddress,
    fullAddress: `${row.province} ${row.city} ${row.district} ${row.detailAddress}`,
    isDefault: row.isDefault
  }

  emit('select', addressInfo)
  dialogVisible.value = false
}

// 暴露方法
defineExpose({ open })
</script>

<style scoped>
.address-selector {
  min-height: 400px;
}

.search-area {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
}
</style>